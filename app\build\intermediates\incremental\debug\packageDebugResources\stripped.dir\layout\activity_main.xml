<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.QrScanActivity">

    <!-- 摄像头预览组件 -->
    <androidx.camera.view.PreviewView
        android:id="@+id/preview_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/control_panel"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" >
    </androidx.camera.view.PreviewView>

    <!-- 二维码覆盖层 -->
    <com.yancao.qrscanner.ui.QrCodeOverlayView
        android:id="@+id/qr_overlay_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@+id/preview_view"
        app:layout_constraintEnd_toEndOf="@+id/preview_view"
        app:layout_constraintStart_toStartOf="@+id/preview_view"
        app:layout_constraintTop_toTopOf="@+id/preview_view" />

    <!-- 扫描结果显示区域 -->

    <!-- 闪光灯按钮 - 浮动在预览视图右上角 -->
    <Button
        android:id="@+id/btn_flashlight"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:text="开启闪光灯"
        android:textSize="12sp"
        android:background="@android:color/holo_orange_light"
        android:textColor="@android:color/white"
        android:padding="8dp"
        app:layout_constraintEnd_toEndOf="@+id/preview_view"
        app:layout_constraintTop_toTopOf="@+id/preview_view"
        android:visibility="gone"/>

    <!-- 曝光补偿控制面板 - 位于预览视图右边居中 -->
    <LinearLayout
        android:id="@+id/exposure_control_panel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="#80000000"
        android:padding="12dp"
        android:layout_marginEnd="16dp"
        app:layout_constraintEnd_toEndOf="@+id/preview_view"
        app:layout_constraintTop_toTopOf="@+id/preview_view"
        app:layout_constraintBottom_toBottomOf="@+id/preview_view"
        android:visibility="visible">

        <!-- 曝光补偿标题 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="曝光"
            android:textColor="@android:color/white"
            android:textSize="12sp"
            android:textStyle="bold"
            android:layout_gravity="center"
            android:layout_marginBottom="8dp" />

        <!-- 曝光补偿值显示 -->
        <TextView
            android:id="@+id/tv_exposure_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="0"
            android:textColor="@android:color/white"
            android:textSize="14sp"
            android:textStyle="bold"
            android:layout_gravity="center"
            android:layout_marginBottom="8dp" />

        <!-- 竖直方向的曝光补偿滑动条 -->
        <SeekBar
            android:id="@+id/seek_bar_exposure"
            android:layout_width="wrap_content"
            android:layout_height="100dp"
            android:rotation="270"
            android:max="100"
            android:progress="50"
            android:progressTint="@android:color/holo_orange_light"
            android:thumbTint="@android:color/holo_orange_light"
            android:layout_gravity="center" />

        <!-- 曝光补偿说明 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="↑亮\n↓暗"
            android:textColor="@android:color/white"
            android:textSize="10sp"
            android:layout_gravity="center"
            android:layout_marginTop="8dp"
            android:gravity="center" />

    </LinearLayout>

    <!-- 新的缩放控制面板 - 位于control_panel上方居中 -->
    <FrameLayout
        android:id="@+id/zoom_control_panel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:layout_constraintBottom_toTopOf="@+id/control_panel"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <!-- 滑动条容器 - 初始隐藏 -->
        <LinearLayout
            android:id="@+id/zoom_slider_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_gravity="center"
            android:background="#80000000"
            android:padding="16dp"
            android:visibility="gone">

            <!-- 当前缩放值显示 -->
            <TextView
                android:id="@+id/tv_zoom_ratio"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="缩放: 1.0x"
                android:textColor="@android:color/white"
                android:textSize="14sp"
                android:textStyle="bold"
                android:layout_gravity="center"
                android:layout_marginBottom="8dp" />

            <!-- 缩放滑动条 -->
            <SeekBar
                android:id="@+id/seek_bar_zoom"
                android:layout_width="200dp"
                android:layout_height="wrap_content"
                android:max="100"
                android:progress="0"
                android:progressTint="@android:color/white"
                android:thumbTint="@android:color/white" />

        </LinearLayout>

    </FrameLayout>

    <!-- 控制面板 -->
    <LinearLayout
        android:id="@+id/control_panel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@android:color/black"
        android:padding="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <!-- 按钮容器 -->
        <LinearLayout
            android:id="@+id/button_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <!-- 扫描按钮 -->
            <Button
                android:id="@+id/btn_scan"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:text="开始扫描"
                android:background="@android:color/holo_blue_light"
                android:textColor="@android:color/white" />

            <!-- 广播按钮 -->
            <Button
                android:id="@+id/btn_broadcast"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:text="广播结果"
                android:background="@android:color/holo_green_light"
                android:textColor="@android:color/white" />

        </LinearLayout>

    </LinearLayout>
    <TextView
        android:id="@+id/tv_scan_results"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="扫描结果将显示在这里"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginTop="16dp"
        android:layout_marginStart="16dp" />

</androidx.constraintlayout.widget.ConstraintLayout>