  Manifest android  CAMERA android.Manifest.permission  Activity android.app  Application android.app  ActivityMainBinding android.app.Activity  ActivityResultsBinding android.app.Activity  BitmapSizeReduce android.app.Activity  DisplayUtils android.app.Activity  ImageHolder android.app.Activity  Intent android.app.Activity  Log android.app.Activity  QRCodeAnalyzer android.app.Activity  RequestPermission android.app.Activity  ResultsActivity android.app.Activity  Toast android.app.Activity  getAppUsableScreenSize android.app.Activity  getValue android.app.Activity  java android.app.Activity  joinToString android.app.Activity  layoutInflater android.app.Activity  let android.app.Activity  
mapIndexed android.app.Activity  onCreate android.app.Activity  provideDelegate android.app.Activity  resizeBitmap android.app.Activity  
startActivity android.app.Activity  
viewModels android.app.Activity  
windowManager android.app.Activity  Context android.content  Intent android.content  ActivityMainBinding android.content.Context  ActivityResultsBinding android.content.Context  BitmapSizeReduce android.content.Context  DisplayUtils android.content.Context  ImageHolder android.content.Context  Intent android.content.Context  Log android.content.Context  QRCodeAnalyzer android.content.Context  RequestPermission android.content.Context  ResultsActivity android.content.Context  Toast android.content.Context  getAppUsableScreenSize android.content.Context  getValue android.content.Context  java android.content.Context  joinToString android.content.Context  let android.content.Context  
mapIndexed android.content.Context  provideDelegate android.content.Context  resizeBitmap android.content.Context  
viewModels android.content.Context  ActivityMainBinding android.content.ContextWrapper  ActivityResultsBinding android.content.ContextWrapper  BitmapSizeReduce android.content.ContextWrapper  DisplayUtils android.content.ContextWrapper  ImageHolder android.content.ContextWrapper  Intent android.content.ContextWrapper  Log android.content.ContextWrapper  QRCodeAnalyzer android.content.ContextWrapper  RequestPermission android.content.ContextWrapper  ResultsActivity android.content.ContextWrapper  Toast android.content.ContextWrapper  getAppUsableScreenSize android.content.ContextWrapper  getValue android.content.ContextWrapper  java android.content.ContextWrapper  joinToString android.content.ContextWrapper  let android.content.ContextWrapper  
mapIndexed android.content.ContextWrapper  provideDelegate android.content.ContextWrapper  resizeBitmap android.content.ContextWrapper  
viewModels android.content.ContextWrapper  PackageManager android.content.pm  PERMISSION_GRANTED !android.content.pm.PackageManager  Bitmap android.graphics  
BitmapFactory android.graphics  Matrix android.graphics  Point android.graphics  Rect android.graphics  createBitmap android.graphics.Bitmap  height android.graphics.Bitmap  let android.graphics.Bitmap  scale android.graphics.Bitmap  width android.graphics.Bitmap  decodeByteArray android.graphics.BitmapFactory  
postRotate android.graphics.Matrix  x android.graphics.Point  y android.graphics.Point  height android.graphics.Rect  width android.graphics.Rect  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  R android.os.Build.VERSION_CODES  DisplayMetrics android.util  Log android.util  heightPixels android.util.DisplayMetrics  widthPixels android.util.DisplayMetrics  d android.util.Log  e android.util.Log  LayoutInflater android.view  
WindowMetrics android.view  ActivityMainBinding  android.view.ContextThemeWrapper  ActivityResultsBinding  android.view.ContextThemeWrapper  BitmapSizeReduce  android.view.ContextThemeWrapper  DisplayUtils  android.view.ContextThemeWrapper  ImageHolder  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  Log  android.view.ContextThemeWrapper  QRCodeAnalyzer  android.view.ContextThemeWrapper  RequestPermission  android.view.ContextThemeWrapper  ResultsActivity  android.view.ContextThemeWrapper  Toast  android.view.ContextThemeWrapper  getAppUsableScreenSize  android.view.ContextThemeWrapper  getValue  android.view.ContextThemeWrapper  java  android.view.ContextThemeWrapper  joinToString  android.view.ContextThemeWrapper  let  android.view.ContextThemeWrapper  
mapIndexed  android.view.ContextThemeWrapper  provideDelegate  android.view.ContextThemeWrapper  resizeBitmap  android.view.ContextThemeWrapper  
viewModels  android.view.ContextThemeWrapper  
getMetrics android.view.Display  OnClickListener android.view.View  setOnClickListener android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnClickListener  currentWindowMetrics android.view.WindowManager  defaultDisplay android.view.WindowManager  bounds android.view.WindowMetrics  Button android.widget  FrameLayout android.widget  	ImageView android.widget  TextView android.widget  Toast android.widget  setOnClickListener android.widget.Button  setImageBitmap android.widget.ImageView  text android.widget.TextView  LENGTH_LONG android.widget.Toast  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  ComponentActivity androidx.activity  
viewModels androidx.activity  ActivityMainBinding #androidx.activity.ComponentActivity  ActivityResultsBinding #androidx.activity.ComponentActivity  BitmapSizeReduce #androidx.activity.ComponentActivity  Boolean #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  DisplayUtils #androidx.activity.ComponentActivity  ImageHolder #androidx.activity.ComponentActivity  Intent #androidx.activity.ComponentActivity  Log #androidx.activity.ComponentActivity  QRCodeAnalyzer #androidx.activity.ComponentActivity  QrScanViewModel #androidx.activity.ComponentActivity  RequestPermission #androidx.activity.ComponentActivity  ResultsActivity #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  Toast #androidx.activity.ComponentActivity  getAppUsableScreenSize #androidx.activity.ComponentActivity  getValue #androidx.activity.ComponentActivity  java #androidx.activity.ComponentActivity  joinToString #androidx.activity.ComponentActivity  let #androidx.activity.ComponentActivity  
mapIndexed #androidx.activity.ComponentActivity  provideDelegate #androidx.activity.ComponentActivity  registerForActivityResult #androidx.activity.ComponentActivity  resizeBitmap #androidx.activity.ComponentActivity  
viewModels #androidx.activity.ComponentActivity  ActivityMainBinding -androidx.activity.ComponentActivity.Companion  ActivityResultsBinding -androidx.activity.ComponentActivity.Companion  BitmapSizeReduce -androidx.activity.ComponentActivity.Companion  DisplayUtils -androidx.activity.ComponentActivity.Companion  ImageHolder -androidx.activity.ComponentActivity.Companion  Intent -androidx.activity.ComponentActivity.Companion  Log -androidx.activity.ComponentActivity.Companion  QRCodeAnalyzer -androidx.activity.ComponentActivity.Companion  RequestPermission -androidx.activity.ComponentActivity.Companion  ResultsActivity -androidx.activity.ComponentActivity.Companion  Toast -androidx.activity.ComponentActivity.Companion  getAppUsableScreenSize -androidx.activity.ComponentActivity.Companion  getValue -androidx.activity.ComponentActivity.Companion  java -androidx.activity.ComponentActivity.Companion  joinToString -androidx.activity.ComponentActivity.Companion  let -androidx.activity.ComponentActivity.Companion  
mapIndexed -androidx.activity.ComponentActivity.Companion  provideDelegate -androidx.activity.ComponentActivity.Companion  resizeBitmap -androidx.activity.ComponentActivity.Companion  
viewModels -androidx.activity.ComponentActivity.Companion  ActivityResultCallback androidx.activity.result  ActivityResultLauncher androidx.activity.result  <SAM-CONSTRUCTOR> /androidx.activity.result.ActivityResultCallback  launch /androidx.activity.result.ActivityResultLauncher  ActivityResultContracts !androidx.activity.result.contract  RequestPermission 9androidx.activity.result.contract.ActivityResultContracts  AppCompatActivity androidx.appcompat.app  ActivityMainBinding (androidx.appcompat.app.AppCompatActivity  ActivityResultsBinding (androidx.appcompat.app.AppCompatActivity  BitmapSizeReduce (androidx.appcompat.app.AppCompatActivity  DisplayUtils (androidx.appcompat.app.AppCompatActivity  ImageHolder (androidx.appcompat.app.AppCompatActivity  Intent (androidx.appcompat.app.AppCompatActivity  Log (androidx.appcompat.app.AppCompatActivity  QRCodeAnalyzer (androidx.appcompat.app.AppCompatActivity  RequestPermission (androidx.appcompat.app.AppCompatActivity  ResultsActivity (androidx.appcompat.app.AppCompatActivity  Toast (androidx.appcompat.app.AppCompatActivity  getAppUsableScreenSize (androidx.appcompat.app.AppCompatActivity  getValue (androidx.appcompat.app.AppCompatActivity  java (androidx.appcompat.app.AppCompatActivity  joinToString (androidx.appcompat.app.AppCompatActivity  let (androidx.appcompat.app.AppCompatActivity  
mapIndexed (androidx.appcompat.app.AppCompatActivity  onCreate (androidx.appcompat.app.AppCompatActivity  	onDestroy (androidx.appcompat.app.AppCompatActivity  provideDelegate (androidx.appcompat.app.AppCompatActivity  resizeBitmap (androidx.appcompat.app.AppCompatActivity  setContentView (androidx.appcompat.app.AppCompatActivity  
viewModels (androidx.appcompat.app.AppCompatActivity  Camera androidx.camera.core  CameraSelector androidx.camera.core  ConcurrentCamera androidx.camera.core  ImageCapture androidx.camera.core  ImageCaptureException androidx.camera.core  	ImageInfo androidx.camera.core  
ImageProxy androidx.camera.core  Preview androidx.camera.core  DEFAULT_BACK_CAMERA #androidx.camera.core.CameraSelector  Builder !androidx.camera.core.ImageCapture  CAPTURE_MODE_MAXIMIZE_QUALITY !androidx.camera.core.ImageCapture  OnImageCapturedCallback !androidx.camera.core.ImageCapture  takePicture !androidx.camera.core.ImageCapture  build )androidx.camera.core.ImageCapture.Builder  setCaptureMode )androidx.camera.core.ImageCapture.Builder  
ContextCompat 9androidx.camera.core.ImageCapture.OnImageCapturedCallback  context 9androidx.camera.core.ImageCapture.OnImageCapturedCallback  imageProxyToBitmap 9androidx.camera.core.ImageCapture.OnImageCapturedCallback  printStackTrace *androidx.camera.core.ImageCaptureException  rotationDegrees androidx.camera.core.ImageInfo  close androidx.camera.core.ImageProxy  	imageInfo androidx.camera.core.ImageProxy  planes androidx.camera.core.ImageProxy  buffer *androidx.camera.core.ImageProxy.PlaneProxy  Builder androidx.camera.core.Preview  SurfaceProvider androidx.camera.core.Preview  also androidx.camera.core.Preview  setSurfaceProvider androidx.camera.core.Preview  build $androidx.camera.core.Preview.Builder  ProcessCameraProvider androidx.camera.lifecycle  	Companion /androidx.camera.lifecycle.ProcessCameraProvider  bindToLifecycle /androidx.camera.lifecycle.ProcessCameraProvider  getInstance /androidx.camera.lifecycle.ProcessCameraProvider  	unbindAll /androidx.camera.lifecycle.ProcessCameraProvider  getInstance 9androidx.camera.lifecycle.ProcessCameraProvider.Companion  PreviewView androidx.camera.view  surfaceProvider  androidx.camera.view.PreviewView  ConstraintLayout  androidx.constraintlayout.widget  ActivityMainBinding #androidx.core.app.ComponentActivity  ActivityResultsBinding #androidx.core.app.ComponentActivity  BitmapSizeReduce #androidx.core.app.ComponentActivity  Boolean #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  DisplayUtils #androidx.core.app.ComponentActivity  ImageHolder #androidx.core.app.ComponentActivity  Intent #androidx.core.app.ComponentActivity  Log #androidx.core.app.ComponentActivity  QRCodeAnalyzer #androidx.core.app.ComponentActivity  QrScanViewModel #androidx.core.app.ComponentActivity  RequestPermission #androidx.core.app.ComponentActivity  ResultsActivity #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  Toast #androidx.core.app.ComponentActivity  getAppUsableScreenSize #androidx.core.app.ComponentActivity  getValue #androidx.core.app.ComponentActivity  java #androidx.core.app.ComponentActivity  joinToString #androidx.core.app.ComponentActivity  let #androidx.core.app.ComponentActivity  
mapIndexed #androidx.core.app.ComponentActivity  provideDelegate #androidx.core.app.ComponentActivity  resizeBitmap #androidx.core.app.ComponentActivity  
viewModels #androidx.core.app.ComponentActivity  
ContextCompat androidx.core.content  checkSelfPermission #androidx.core.content.ContextCompat  getMainExecutor #androidx.core.content.ContextCompat  scale androidx.core.graphics  ActivityMainBinding &androidx.fragment.app.FragmentActivity  ActivityResultsBinding &androidx.fragment.app.FragmentActivity  BitmapSizeReduce &androidx.fragment.app.FragmentActivity  DisplayUtils &androidx.fragment.app.FragmentActivity  ImageHolder &androidx.fragment.app.FragmentActivity  Intent &androidx.fragment.app.FragmentActivity  Log &androidx.fragment.app.FragmentActivity  QRCodeAnalyzer &androidx.fragment.app.FragmentActivity  RequestPermission &androidx.fragment.app.FragmentActivity  ResultsActivity &androidx.fragment.app.FragmentActivity  Toast &androidx.fragment.app.FragmentActivity  getAppUsableScreenSize &androidx.fragment.app.FragmentActivity  getValue &androidx.fragment.app.FragmentActivity  java &androidx.fragment.app.FragmentActivity  joinToString &androidx.fragment.app.FragmentActivity  let &androidx.fragment.app.FragmentActivity  
mapIndexed &androidx.fragment.app.FragmentActivity  onCreate &androidx.fragment.app.FragmentActivity  provideDelegate &androidx.fragment.app.FragmentActivity  resizeBitmap &androidx.fragment.app.FragmentActivity  
viewModels &androidx.fragment.app.FragmentActivity  AndroidViewModel androidx.lifecycle  LifecycleOwner androidx.lifecycle  MutableLiveData androidx.lifecycle  Observer androidx.lifecycle  	onCleared #androidx.lifecycle.AndroidViewModel  observe "androidx.lifecycle.MutableLiveData  	postValue "androidx.lifecycle.MutableLiveData  <SAM-CONSTRUCTOR> androidx.lifecycle.Observer  Bitmap androidx.lifecycle.ViewModel  
CameraManager androidx.lifecycle.ViewModel  MutableLiveData androidx.lifecycle.ViewModel  	onCleared androidx.lifecycle.ViewModel  OnFailureListener com.google.android.gms.tasks  OnSuccessListener com.google.android.gms.tasks  Task com.google.android.gms.tasks  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnFailureListener  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnSuccessListener  addOnFailureListener !com.google.android.gms.tasks.Task  addOnSuccessListener !com.google.android.gms.tasks.Task  ListenableFuture !com.google.common.util.concurrent  addListener 2com.google.common.util.concurrent.ListenableFuture  get 2com.google.common.util.concurrent.ListenableFuture  BarcodeScanner com.google.mlkit.vision.barcode  BarcodeScannerOptions com.google.mlkit.vision.barcode  BarcodeScanning com.google.mlkit.vision.barcode  process .com.google.mlkit.vision.barcode.BarcodeScanner  Builder 5com.google.mlkit.vision.barcode.BarcodeScannerOptions  build =com.google.mlkit.vision.barcode.BarcodeScannerOptions.Builder  setBarcodeFormats =com.google.mlkit.vision.barcode.BarcodeScannerOptions.Builder  	getClient /com.google.mlkit.vision.barcode.BarcodeScanning  Barcode &com.google.mlkit.vision.barcode.common  FORMAT_QR_CODE .com.google.mlkit.vision.barcode.common.Barcode  rawValue .com.google.mlkit.vision.barcode.common.Barcode  
InputImage com.google.mlkit.vision.common  
fromBitmap )com.google.mlkit.vision.common.InputImage  QrScanRepository com.yancao.qrscanner  Barcode com.yancao.qrscanner.camera  BarcodeScannerOptions com.yancao.qrscanner.camera  BarcodeScanning com.yancao.qrscanner.camera  Bitmap com.yancao.qrscanner.camera  
BitmapFactory com.yancao.qrscanner.camera  	ByteArray com.yancao.qrscanner.camera  
CameraManager com.yancao.qrscanner.camera  CameraSelector com.yancao.qrscanner.camera  Context com.yancao.qrscanner.camera  
ContextCompat com.yancao.qrscanner.camera  	Exception com.yancao.qrscanner.camera  	Executors com.yancao.qrscanner.camera  ImageCapture com.yancao.qrscanner.camera  ImageCaptureException com.yancao.qrscanner.camera  
ImageProxy com.yancao.qrscanner.camera  
InputImage com.yancao.qrscanner.camera  LifecycleOwner com.yancao.qrscanner.camera  List com.yancao.qrscanner.camera  Log com.yancao.qrscanner.camera  Matrix com.yancao.qrscanner.camera  Preview com.yancao.qrscanner.camera  PreviewView com.yancao.qrscanner.camera  ProcessCameraProvider com.yancao.qrscanner.camera  QRCodeAnalyzer com.yancao.qrscanner.camera  Unit com.yancao.qrscanner.camera  also com.yancao.qrscanner.camera  context com.yancao.qrscanner.camera  getInstance com.yancao.qrscanner.camera  imageProxyToBitmap com.yancao.qrscanner.camera  Bitmap )com.yancao.qrscanner.camera.CameraManager  
BitmapFactory )com.yancao.qrscanner.camera.CameraManager  	ByteArray )com.yancao.qrscanner.camera.CameraManager  CameraSelector )com.yancao.qrscanner.camera.CameraManager  
ContextCompat )com.yancao.qrscanner.camera.CameraManager  	Executors )com.yancao.qrscanner.camera.CameraManager  ImageCapture )com.yancao.qrscanner.camera.CameraManager  Matrix )com.yancao.qrscanner.camera.CameraManager  Preview )com.yancao.qrscanner.camera.CameraManager  ProcessCameraProvider )com.yancao.qrscanner.camera.CameraManager  also )com.yancao.qrscanner.camera.CameraManager  cameraExecutor )com.yancao.qrscanner.camera.CameraManager  cameraProvider )com.yancao.qrscanner.camera.CameraManager  context )com.yancao.qrscanner.camera.CameraManager  getInstance )com.yancao.qrscanner.camera.CameraManager  imageCapture )com.yancao.qrscanner.camera.CameraManager  imageProxyToBitmap )com.yancao.qrscanner.camera.CameraManager  shutdown )com.yancao.qrscanner.camera.CameraManager  startCamera )com.yancao.qrscanner.camera.CameraManager  takePicture )com.yancao.qrscanner.camera.CameraManager  OnImageCapturedCallback (com.yancao.qrscanner.camera.ImageCapture  Barcode *com.yancao.qrscanner.camera.QRCodeAnalyzer  BarcodeScannerOptions *com.yancao.qrscanner.camera.QRCodeAnalyzer  BarcodeScanning *com.yancao.qrscanner.camera.QRCodeAnalyzer  
InputImage *com.yancao.qrscanner.camera.QRCodeAnalyzer  Log *com.yancao.qrscanner.camera.QRCodeAnalyzer  analyze *com.yancao.qrscanner.camera.QRCodeAnalyzer  options *com.yancao.qrscanner.camera.QRCodeAnalyzer  scanner *com.yancao.qrscanner.camera.QRCodeAnalyzer  ActivityMainBinding  com.yancao.qrscanner.databinding  ActivityResultsBinding  com.yancao.qrscanner.databinding  btnTakePicture 4com.yancao.qrscanner.databinding.ActivityMainBinding  inflate 4com.yancao.qrscanner.databinding.ActivityMainBinding  previewView 4com.yancao.qrscanner.databinding.ActivityMainBinding  root 4com.yancao.qrscanner.databinding.ActivityMainBinding  inflate 7com.yancao.qrscanner.databinding.ActivityResultsBinding  ivCapturedImage 7com.yancao.qrscanner.databinding.ActivityResultsBinding  resultsDisplay 7com.yancao.qrscanner.databinding.ActivityResultsBinding  root 7com.yancao.qrscanner.databinding.ActivityResultsBinding  ActivityMainBinding com.yancao.qrscanner.ui  ActivityResultsBinding com.yancao.qrscanner.ui  AppCompatActivity com.yancao.qrscanner.ui  BitmapSizeReduce com.yancao.qrscanner.ui  Boolean com.yancao.qrscanner.ui  Bundle com.yancao.qrscanner.ui  DisplayUtils com.yancao.qrscanner.ui  ImageHolder com.yancao.qrscanner.ui  Intent com.yancao.qrscanner.ui  Log com.yancao.qrscanner.ui  QRCodeAnalyzer com.yancao.qrscanner.ui  QrScanActivity com.yancao.qrscanner.ui  QrScanViewModel com.yancao.qrscanner.ui  RequestPermission com.yancao.qrscanner.ui  ResultsActivity com.yancao.qrscanner.ui  String com.yancao.qrscanner.ui  Toast com.yancao.qrscanner.ui  getAppUsableScreenSize com.yancao.qrscanner.ui  getValue com.yancao.qrscanner.ui  java com.yancao.qrscanner.ui  joinToString com.yancao.qrscanner.ui  let com.yancao.qrscanner.ui  
mapIndexed com.yancao.qrscanner.ui  provideDelegate com.yancao.qrscanner.ui  resizeBitmap com.yancao.qrscanner.ui  ActivityMainBinding &com.yancao.qrscanner.ui.QrScanActivity  ImageHolder &com.yancao.qrscanner.ui.QrScanActivity  Intent &com.yancao.qrscanner.ui.QrScanActivity  RequestPermission &com.yancao.qrscanner.ui.QrScanActivity  ResultsActivity &com.yancao.qrscanner.ui.QrScanActivity  Toast &com.yancao.qrscanner.ui.QrScanActivity  binding &com.yancao.qrscanner.ui.QrScanActivity  getValue &com.yancao.qrscanner.ui.QrScanActivity  java &com.yancao.qrscanner.ui.QrScanActivity  layoutInflater &com.yancao.qrscanner.ui.QrScanActivity  permissionHelper &com.yancao.qrscanner.ui.QrScanActivity  provideDelegate &com.yancao.qrscanner.ui.QrScanActivity  setContentView &com.yancao.qrscanner.ui.QrScanActivity  
startActivity &com.yancao.qrscanner.ui.QrScanActivity  	viewModel &com.yancao.qrscanner.ui.QrScanActivity  
viewModels &com.yancao.qrscanner.ui.QrScanActivity  ActivityResultsBinding 'com.yancao.qrscanner.ui.ResultsActivity  BitmapSizeReduce 'com.yancao.qrscanner.ui.ResultsActivity  DisplayUtils 'com.yancao.qrscanner.ui.ResultsActivity  ImageHolder 'com.yancao.qrscanner.ui.ResultsActivity  Log 'com.yancao.qrscanner.ui.ResultsActivity  QRCodeAnalyzer 'com.yancao.qrscanner.ui.ResultsActivity  Toast 'com.yancao.qrscanner.ui.ResultsActivity  binding 'com.yancao.qrscanner.ui.ResultsActivity  getAppUsableScreenSize 'com.yancao.qrscanner.ui.ResultsActivity  joinToString 'com.yancao.qrscanner.ui.ResultsActivity  layoutInflater 'com.yancao.qrscanner.ui.ResultsActivity  let 'com.yancao.qrscanner.ui.ResultsActivity  
mapIndexed 'com.yancao.qrscanner.ui.ResultsActivity  qrCodeAnalyzer 'com.yancao.qrscanner.ui.ResultsActivity  resizeBitmap 'com.yancao.qrscanner.ui.ResultsActivity  setContentView 'com.yancao.qrscanner.ui.ResultsActivity  	showToast 'com.yancao.qrscanner.ui.ResultsActivity  Activity com.yancao.qrscanner.utils  ActivityResultContracts com.yancao.qrscanner.utils  Bitmap com.yancao.qrscanner.utils  BitmapSizeReduce com.yancao.qrscanner.utils  Build com.yancao.qrscanner.utils  ComponentActivity com.yancao.qrscanner.utils  
ContextCompat com.yancao.qrscanner.utils  DisplayMetrics com.yancao.qrscanner.utils  DisplayUtils com.yancao.qrscanner.utils  ImageHolder com.yancao.qrscanner.utils  Int com.yancao.qrscanner.utils  Manifest com.yancao.qrscanner.utils  PackageManager com.yancao.qrscanner.utils  Point com.yancao.qrscanner.utils  RequestPermission com.yancao.qrscanner.utils  Suppress com.yancao.qrscanner.utils  Unit com.yancao.qrscanner.utils  min com.yancao.qrscanner.utils  scale com.yancao.qrscanner.utils  min +com.yancao.qrscanner.utils.BitmapSizeReduce  resizeBitmap +com.yancao.qrscanner.utils.BitmapSizeReduce  scale +com.yancao.qrscanner.utils.BitmapSizeReduce  Activity 'com.yancao.qrscanner.utils.DisplayUtils  Build 'com.yancao.qrscanner.utils.DisplayUtils  	Companion 'com.yancao.qrscanner.utils.DisplayUtils  DisplayMetrics 'com.yancao.qrscanner.utils.DisplayUtils  Point 'com.yancao.qrscanner.utils.DisplayUtils  Suppress 'com.yancao.qrscanner.utils.DisplayUtils  getAppUsableScreenSize 'com.yancao.qrscanner.utils.DisplayUtils  Build 1com.yancao.qrscanner.utils.DisplayUtils.Companion  DisplayMetrics 1com.yancao.qrscanner.utils.DisplayUtils.Companion  Point 1com.yancao.qrscanner.utils.DisplayUtils.Companion  getAppUsableScreenSize 1com.yancao.qrscanner.utils.DisplayUtils.Companion  bitmap &com.yancao.qrscanner.utils.ImageHolder  ActivityResultContracts ,com.yancao.qrscanner.utils.RequestPermission  
ContextCompat ,com.yancao.qrscanner.utils.RequestPermission  Manifest ,com.yancao.qrscanner.utils.RequestPermission  PackageManager ,com.yancao.qrscanner.utils.RequestPermission  activity ,com.yancao.qrscanner.utils.RequestPermission  checkAndRequestCameraPermission ,com.yancao.qrscanner.utils.RequestPermission  onPermissionDenied ,com.yancao.qrscanner.utils.RequestPermission  onPermissionGranted ,com.yancao.qrscanner.utils.RequestPermission  permissionLauncher ,com.yancao.qrscanner.utils.RequestPermission  AndroidViewModel com.yancao.qrscanner.viewModel  Application com.yancao.qrscanner.viewModel  Bitmap com.yancao.qrscanner.viewModel  
CameraManager com.yancao.qrscanner.viewModel  LifecycleOwner com.yancao.qrscanner.viewModel  MutableLiveData com.yancao.qrscanner.viewModel  PreviewView com.yancao.qrscanner.viewModel  QrScanViewModel com.yancao.qrscanner.viewModel  
CameraManager .com.yancao.qrscanner.viewModel.QrScanViewModel  MutableLiveData .com.yancao.qrscanner.viewModel.QrScanViewModel  
cameraManager .com.yancao.qrscanner.viewModel.QrScanViewModel  captureImageAndSave .com.yancao.qrscanner.viewModel.QrScanViewModel  capturedImageBitmap .com.yancao.qrscanner.viewModel.QrScanViewModel  startCamera .com.yancao.qrscanner.viewModel.QrScanViewModel  Class 	java.lang  	Exception 	java.lang  Runnable 	java.lang  message java.lang.Exception  printStackTrace java.lang.Exception  <SAM-CONSTRUCTOR> java.lang.Runnable  
ByteBuffer java.nio  	remaining java.nio.Buffer  get java.nio.ByteBuffer  	remaining java.nio.ByteBuffer  Executor java.util.concurrent  	Executors java.util.concurrent  execute java.util.concurrent.Executor  shutdown $java.util.concurrent.ExecutorService  newSingleThreadExecutor java.util.concurrent.Executors  	ByteArray kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  Lazy kotlin  Suppress kotlin  also kotlin  getValue kotlin  let kotlin  get kotlin.Array  size kotlin.ByteArray  	compareTo kotlin.Float  div kotlin.Float  toInt kotlin.Float  invoke kotlin.Function0  invoke kotlin.Function1  	compareTo 
kotlin.Int  plus 
kotlin.Int  times 
kotlin.Int  toFloat 
kotlin.Int  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  message kotlin.Throwable  printStackTrace kotlin.Throwable  List kotlin.collections  getValue kotlin.collections  joinToString kotlin.collections  
mapIndexed kotlin.collections  min kotlin.collections  isEmpty kotlin.collections.List  joinToString kotlin.collections.List  
mapIndexed kotlin.collections.List  size kotlin.collections.List  java 
kotlin.jvm  min kotlin.math  
KProperty1 kotlin.reflect  java kotlin.reflect.KClass  Sequence kotlin.sequences  joinToString kotlin.sequences  
mapIndexed kotlin.sequences  min kotlin.sequences  
mapIndexed kotlin.text  min kotlin.text  BroadCastUtils android.app.Activity  Handler android.app.Activity  Looper android.app.Activity  ScanResultsHolder android.app.Activity  apply android.app.Activity  finish android.app.Activity  firstOrNull android.app.Activity  getLatestResult android.app.Activity  intent android.app.Activity  
isNotEmpty android.app.Activity  takeLast android.app.Activity  BroadCastUtils android.content.Context  Handler android.content.Context  Looper android.content.Context  ScanResultsHolder android.content.Context  apply android.content.Context  firstOrNull android.content.Context  getLatestResult android.content.Context  
isNotEmpty android.content.Context  takeLast android.content.Context  BroadCastUtils android.content.ContextWrapper  Handler android.content.ContextWrapper  Looper android.content.ContextWrapper  ScanResultsHolder android.content.ContextWrapper  apply android.content.ContextWrapper  firstOrNull android.content.ContextWrapper  getLatestResult android.content.ContextWrapper  
isNotEmpty android.content.ContextWrapper  
sendBroadcast android.content.ContextWrapper  takeLast android.content.ContextWrapper  BroadCastUtils android.content.Intent  apply android.content.Intent  firstOrNull android.content.Intent  getBooleanExtra android.content.Intent  putExtra android.content.Intent  Image 
android.media  Handler 
android.os  Looper 
android.os  postDelayed android.os.Handler  
getMainLooper android.os.Looper  BroadCastUtils  android.view.ContextThemeWrapper  Handler  android.view.ContextThemeWrapper  Looper  android.view.ContextThemeWrapper  ScanResultsHolder  android.view.ContextThemeWrapper  apply  android.view.ContextThemeWrapper  firstOrNull  android.view.ContextThemeWrapper  getLatestResult  android.view.ContextThemeWrapper  
isNotEmpty  android.view.ContextThemeWrapper  takeLast  android.view.ContextThemeWrapper  text android.widget.Button  BroadCastUtils #androidx.activity.ComponentActivity  Handler #androidx.activity.ComponentActivity  Looper #androidx.activity.ComponentActivity  ScanResultsHolder #androidx.activity.ComponentActivity  apply #androidx.activity.ComponentActivity  firstOrNull #androidx.activity.ComponentActivity  getLatestResult #androidx.activity.ComponentActivity  
isNotEmpty #androidx.activity.ComponentActivity  takeLast #androidx.activity.ComponentActivity  BroadCastUtils -androidx.activity.ComponentActivity.Companion  Handler -androidx.activity.ComponentActivity.Companion  Looper -androidx.activity.ComponentActivity.Companion  ScanResultsHolder -androidx.activity.ComponentActivity.Companion  apply -androidx.activity.ComponentActivity.Companion  firstOrNull -androidx.activity.ComponentActivity.Companion  getLatestResult -androidx.activity.ComponentActivity.Companion  
isNotEmpty -androidx.activity.ComponentActivity.Companion  takeLast -androidx.activity.ComponentActivity.Companion  OptIn androidx.annotation  BroadCastUtils (androidx.appcompat.app.AppCompatActivity  Handler (androidx.appcompat.app.AppCompatActivity  Looper (androidx.appcompat.app.AppCompatActivity  ScanResultsHolder (androidx.appcompat.app.AppCompatActivity  apply (androidx.appcompat.app.AppCompatActivity  firstOrNull (androidx.appcompat.app.AppCompatActivity  getLatestResult (androidx.appcompat.app.AppCompatActivity  
isNotEmpty (androidx.appcompat.app.AppCompatActivity  takeLast (androidx.appcompat.app.AppCompatActivity  ExperimentalGetImage androidx.camera.core  
ImageAnalysis androidx.camera.core  UseCase androidx.camera.core  Analyzer "androidx.camera.core.ImageAnalysis  Builder "androidx.camera.core.ImageAnalysis  STRATEGY_KEEP_ONLY_LATEST "androidx.camera.core.ImageAnalysis  also "androidx.camera.core.ImageAnalysis  setAnalyzer "androidx.camera.core.ImageAnalysis  build *androidx.camera.core.ImageAnalysis.Builder  setBackpressureStrategy *androidx.camera.core.ImageAnalysis.Builder  image androidx.camera.core.ImageProxy  BroadCastUtils #androidx.core.app.ComponentActivity  Handler #androidx.core.app.ComponentActivity  Looper #androidx.core.app.ComponentActivity  ScanResultsHolder #androidx.core.app.ComponentActivity  apply #androidx.core.app.ComponentActivity  firstOrNull #androidx.core.app.ComponentActivity  getLatestResult #androidx.core.app.ComponentActivity  
isNotEmpty #androidx.core.app.ComponentActivity  takeLast #androidx.core.app.ComponentActivity  BroadCastUtils &androidx.fragment.app.FragmentActivity  Handler &androidx.fragment.app.FragmentActivity  Looper &androidx.fragment.app.FragmentActivity  ScanResultsHolder &androidx.fragment.app.FragmentActivity  apply &androidx.fragment.app.FragmentActivity  firstOrNull &androidx.fragment.app.FragmentActivity  getLatestResult &androidx.fragment.app.FragmentActivity  
isNotEmpty &androidx.fragment.app.FragmentActivity  takeLast &androidx.fragment.app.FragmentActivity  value androidx.lifecycle.LiveData  value "androidx.lifecycle.MutableLiveData  Boolean androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  ScanResultsHolder androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  clearResults androidx.lifecycle.ViewModel  	emptyList androidx.lifecycle.ViewModel  OnCompleteListener com.google.android.gms.tasks  <SAM-CONSTRUCTOR> /com.google.android.gms.tasks.OnCompleteListener  addOnCompleteListener !com.google.android.gms.tasks.Task  fromMediaImage )com.google.mlkit.vision.common.InputImage  Boolean com.yancao.qrscanner.camera  ExperimentalGetImage com.yancao.qrscanner.camera  
ImageAnalysis com.yancao.qrscanner.camera  OptIn com.yancao.qrscanner.camera  RealtimeQRAnalyzer com.yancao.qrscanner.camera  ScanResultsHolder com.yancao.qrscanner.camera  String com.yancao.qrscanner.camera  System com.yancao.qrscanner.camera  addScanResults com.yancao.qrscanner.camera  androidx com.yancao.qrscanner.camera  
isNotEmpty com.yancao.qrscanner.camera  
mapNotNull com.yancao.qrscanner.camera  
mutableListOf com.yancao.qrscanner.camera  println com.yancao.qrscanner.camera  toTypedArray com.yancao.qrscanner.camera  
ImageAnalysis )com.yancao.qrscanner.camera.CameraManager  RealtimeQRAnalyzer )com.yancao.qrscanner.camera.CameraManager  
imageAnalyzer )com.yancao.qrscanner.camera.CameraManager  
mutableListOf )com.yancao.qrscanner.camera.CameraManager  
qrAnalyzer )com.yancao.qrscanner.camera.CameraManager  toTypedArray )com.yancao.qrscanner.camera.CameraManager  Analyzer )com.yancao.qrscanner.camera.ImageAnalysis  Barcode .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  BarcodeScannerOptions .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  BarcodeScanning .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  ExperimentalGetImage .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  
InputImage .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  ScanResultsHolder .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  System .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  addScanResults .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  analyzeInterval .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  
isNotEmpty .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  lastAnalyzedTimestamp .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  
mapNotNull .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  onQRCodeDetected .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  options .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  println .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  scanner .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  camera $com.yancao.qrscanner.camera.androidx  core +com.yancao.qrscanner.camera.androidx.camera  UseCase 0com.yancao.qrscanner.camera.androidx.camera.core  btnBroadcast 4com.yancao.qrscanner.databinding.ActivityMainBinding  btnScan 4com.yancao.qrscanner.databinding.ActivityMainBinding  
tvScanResults 4com.yancao.qrscanner.databinding.ActivityMainBinding  Boolean com.yancao.qrscanner.domain  Int com.yancao.qrscanner.domain  List com.yancao.qrscanner.domain  MutableList com.yancao.qrscanner.domain  ScanResultsHolder com.yancao.qrscanner.domain  String com.yancao.qrscanner.domain  Synchronized com.yancao.qrscanner.domain  Volatile com.yancao.qrscanner.domain  forEach com.yancao.qrscanner.domain  
isNotBlank com.yancao.qrscanner.domain  
isNotEmpty com.yancao.qrscanner.domain  
lastOrNull com.yancao.qrscanner.domain  
mutableListOf com.yancao.qrscanner.domain  println com.yancao.qrscanner.domain  toList com.yancao.qrscanner.domain  _scanResults -com.yancao.qrscanner.domain.ScanResultsHolder  addScanResults -com.yancao.qrscanner.domain.ScanResultsHolder  clearResults -com.yancao.qrscanner.domain.ScanResultsHolder  getLatestResult -com.yancao.qrscanner.domain.ScanResultsHolder  
isNotBlank -com.yancao.qrscanner.domain.ScanResultsHolder  
isNotEmpty -com.yancao.qrscanner.domain.ScanResultsHolder  
lastOrNull -com.yancao.qrscanner.domain.ScanResultsHolder  
mutableListOf -com.yancao.qrscanner.domain.ScanResultsHolder  println -com.yancao.qrscanner.domain.ScanResultsHolder  scanResults -com.yancao.qrscanner.domain.ScanResultsHolder  toList -com.yancao.qrscanner.domain.ScanResultsHolder  BroadCastUtils com.yancao.qrscanner.ui  Handler com.yancao.qrscanner.ui  Looper com.yancao.qrscanner.ui  ScanResultsHolder com.yancao.qrscanner.ui  apply com.yancao.qrscanner.ui  firstOrNull com.yancao.qrscanner.ui  getLatestResult com.yancao.qrscanner.ui  
isNotEmpty com.yancao.qrscanner.ui  takeLast com.yancao.qrscanner.ui  BroadCastUtils &com.yancao.qrscanner.ui.QrScanActivity  ScanResultsHolder &com.yancao.qrscanner.ui.QrScanActivity  apply &com.yancao.qrscanner.ui.QrScanActivity  getLatestResult &com.yancao.qrscanner.ui.QrScanActivity  handleBroadcastButtonClick &com.yancao.qrscanner.ui.QrScanActivity  handleScanButtonClick &com.yancao.qrscanner.ui.QrScanActivity  
isNotEmpty &com.yancao.qrscanner.ui.QrScanActivity  joinToString &com.yancao.qrscanner.ui.QrScanActivity  
sendBroadcast &com.yancao.qrscanner.ui.QrScanActivity  takeLast &com.yancao.qrscanner.ui.QrScanActivity  updateScanButtonText &com.yancao.qrscanner.ui.QrScanActivity  updateScanResultsDisplay &com.yancao.qrscanner.ui.QrScanActivity  BroadCastUtils 'com.yancao.qrscanner.ui.ResultsActivity  Handler 'com.yancao.qrscanner.ui.ResultsActivity  Intent 'com.yancao.qrscanner.ui.ResultsActivity  Looper 'com.yancao.qrscanner.ui.ResultsActivity  apply 'com.yancao.qrscanner.ui.ResultsActivity  finish 'com.yancao.qrscanner.ui.ResultsActivity  firstOrNull 'com.yancao.qrscanner.ui.ResultsActivity  intent 'com.yancao.qrscanner.ui.ResultsActivity  
sendBroadcast 'com.yancao.qrscanner.ui.ResultsActivity  BroadCastUtils com.yancao.qrscanner.utils  BROADCAST_ACTION )com.yancao.qrscanner.utils.BroadCastUtils  BROADCAST_CODE_TYPE )com.yancao.qrscanner.utils.BroadCastUtils  BROADCAST_CODE_TYPE_NAME )com.yancao.qrscanner.utils.BroadCastUtils  BROADCAST_DATA_LABEL )com.yancao.qrscanner.utils.BroadCastUtils  BROADCAST_RAW_DATA )com.yancao.qrscanner.utils.BroadCastUtils  BROADCAST_SWITCH )com.yancao.qrscanner.utils.BroadCastUtils  Boolean com.yancao.qrscanner.viewModel  List com.yancao.qrscanner.viewModel  ScanResultsHolder com.yancao.qrscanner.viewModel  String com.yancao.qrscanner.viewModel  clearResults com.yancao.qrscanner.viewModel  	emptyList com.yancao.qrscanner.viewModel  ScanResultsHolder .com.yancao.qrscanner.viewModel.QrScanViewModel  clearResults .com.yancao.qrscanner.viewModel.QrScanViewModel  	emptyList .com.yancao.qrscanner.viewModel.QrScanViewModel  getAllScanResults .com.yancao.qrscanner.viewModel.QrScanViewModel  isScanningEnabled .com.yancao.qrscanner.viewModel.QrScanViewModel  realtimeScanResults .com.yancao.qrscanner.viewModel.QrScanViewModel  startRealtimeScanning .com.yancao.qrscanner.viewModel.QrScanViewModel  stopRealtimeScanning .com.yancao.qrscanner.viewModel.QrScanViewModel  currentTimeMillis java.lang.System  Array kotlin  CharSequence kotlin  Pair kotlin  apply kotlin  toList kotlin  not kotlin.Boolean  toString 
kotlin.Int  	compareTo kotlin.Long  minus kotlin.Long  
isNotBlank 
kotlin.String  plus 
kotlin.String  MutableList kotlin.collections  	emptyList kotlin.collections  firstOrNull kotlin.collections  forEach kotlin.collections  
isNotEmpty kotlin.collections  
lastOrNull kotlin.collections  
mapNotNull kotlin.collections  
mutableListOf kotlin.collections  takeLast kotlin.collections  toList kotlin.collections  toTypedArray kotlin.collections  firstOrNull kotlin.collections.List  takeLast kotlin.collections.List  add kotlin.collections.MutableList  clear kotlin.collections.MutableList  contains kotlin.collections.MutableList  
isNotEmpty kotlin.collections.MutableList  
lastOrNull kotlin.collections.MutableList  size kotlin.collections.MutableList  toList kotlin.collections.MutableList  toTypedArray kotlin.collections.MutableList  println 	kotlin.io  Synchronized 
kotlin.jvm  Volatile 
kotlin.jvm  firstOrNull 
kotlin.ranges  
lastOrNull 
kotlin.ranges  firstOrNull kotlin.sequences  forEach kotlin.sequences  
lastOrNull kotlin.sequences  
mapNotNull kotlin.sequences  toList kotlin.sequences  firstOrNull kotlin.text  forEach kotlin.text  
isNotBlank kotlin.text  
isNotEmpty kotlin.text  
lastOrNull kotlin.text  
mapNotNull kotlin.text  takeLast kotlin.text  toList kotlin.text  ExperimentalGetImage android.app.Activity  ExperimentalGetImage android.content.Context  ExperimentalGetImage android.content.ContextWrapper  AttributeSet android.graphics  Barcode android.graphics  Canvas android.graphics  Color android.graphics  Context android.graphics  Int android.graphics  JvmOverloads android.graphics  Long android.graphics  Paint android.graphics  RectF android.graphics  
ScannedQRInfo android.graphics  String android.graphics  System android.graphics  Typeface android.graphics  View android.graphics  apply android.graphics  forEach android.graphics  indexOfFirst android.graphics  let android.graphics  
mutableListOf android.graphics  	removeAll android.graphics  drawLine android.graphics.Canvas  drawRect android.graphics.Canvas  
drawRoundRect android.graphics.Canvas  drawText android.graphics.Canvas  BLACK android.graphics.Color  GREEN android.graphics.Color  Color android.graphics.Paint  Paint android.graphics.Paint  Style android.graphics.Paint  Typeface android.graphics.Paint  alpha android.graphics.Paint  apply android.graphics.Paint  color android.graphics.Paint  
getTextBounds android.graphics.Paint  isAntiAlias android.graphics.Paint  strokeWidth android.graphics.Paint  style android.graphics.Paint  textSize android.graphics.Paint  typeface android.graphics.Paint  STROKE android.graphics.Paint.Style  bottom android.graphics.Rect  left android.graphics.Rect  let android.graphics.Rect  right android.graphics.Rect  top android.graphics.Rect  DEFAULT_BOLD android.graphics.Typeface  height android.media.Image  width android.media.Image  AttributeSet android.util  View android.view  ExperimentalGetImage  android.view.ContextThemeWrapper  Color android.view.View  Paint android.view.View  Rect android.view.View  RectF android.view.View  
ScannedQRInfo android.view.View  System android.view.View  Typeface android.view.View  apply android.view.View  height android.view.View  indexOfFirst android.view.View  
invalidate android.view.View  let android.view.View  
mutableListOf android.view.View  onDraw android.view.View  	removeAll android.view.View  width android.view.View  ExperimentalGetImage #androidx.activity.ComponentActivity  OptIn #androidx.activity.ComponentActivity  ExperimentalGetImage -androidx.activity.ComponentActivity.Companion  ExperimentalGetImage (androidx.appcompat.app.AppCompatActivity  height  androidx.camera.view.PreviewView  width  androidx.camera.view.PreviewView  ExperimentalGetImage #androidx.core.app.ComponentActivity  OptIn #androidx.core.app.ComponentActivity  ExperimentalGetImage &androidx.fragment.app.FragmentActivity  Barcode androidx.lifecycle.ViewModel  ExperimentalGetImage androidx.lifecycle.ViewModel  Int androidx.lifecycle.ViewModel  Pair androidx.lifecycle.ViewModel  getLatestResult androidx.lifecycle.ViewModel  getResultCount androidx.lifecycle.ViewModel  
hasResults androidx.lifecycle.ViewModel  boundingBox .com.google.mlkit.vision.barcode.common.Barcode  Int com.yancao.qrscanner.camera  onQRCodeWithPosition .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  
qrOverlayView 4com.yancao.qrscanner.databinding.ActivityMainBinding  getResultCount -com.yancao.qrscanner.domain.ScanResultsHolder  
hasResults -com.yancao.qrscanner.domain.ScanResultsHolder  AttributeSet com.yancao.qrscanner.ui  Barcode com.yancao.qrscanner.ui  Canvas com.yancao.qrscanner.ui  Color com.yancao.qrscanner.ui  Context com.yancao.qrscanner.ui  ExperimentalGetImage com.yancao.qrscanner.ui  Int com.yancao.qrscanner.ui  JvmOverloads com.yancao.qrscanner.ui  Long com.yancao.qrscanner.ui  OptIn com.yancao.qrscanner.ui  Paint com.yancao.qrscanner.ui  QrCodeOverlayView com.yancao.qrscanner.ui  Rect com.yancao.qrscanner.ui  RectF com.yancao.qrscanner.ui  
ScannedQRInfo com.yancao.qrscanner.ui  System com.yancao.qrscanner.ui  Typeface com.yancao.qrscanner.ui  View com.yancao.qrscanner.ui  forEach com.yancao.qrscanner.ui  indexOfFirst com.yancao.qrscanner.ui  
mutableListOf com.yancao.qrscanner.ui  	removeAll com.yancao.qrscanner.ui  AttributeSet )com.yancao.qrscanner.ui.QrCodeOverlayView  Barcode )com.yancao.qrscanner.ui.QrCodeOverlayView  Canvas )com.yancao.qrscanner.ui.QrCodeOverlayView  Color )com.yancao.qrscanner.ui.QrCodeOverlayView  Context )com.yancao.qrscanner.ui.QrCodeOverlayView  Int )com.yancao.qrscanner.ui.QrCodeOverlayView  JvmOverloads )com.yancao.qrscanner.ui.QrCodeOverlayView  Long )com.yancao.qrscanner.ui.QrCodeOverlayView  Paint )com.yancao.qrscanner.ui.QrCodeOverlayView  Rect )com.yancao.qrscanner.ui.QrCodeOverlayView  RectF )com.yancao.qrscanner.ui.QrCodeOverlayView  
ScannedQRInfo )com.yancao.qrscanner.ui.QrCodeOverlayView  String )com.yancao.qrscanner.ui.QrCodeOverlayView  System )com.yancao.qrscanner.ui.QrCodeOverlayView  Typeface )com.yancao.qrscanner.ui.QrCodeOverlayView  addScannedQRCode )com.yancao.qrscanner.ui.QrCodeOverlayView  apply )com.yancao.qrscanner.ui.QrCodeOverlayView  clearExpiredQRCodes )com.yancao.qrscanner.ui.QrCodeOverlayView  clearScannedQRCodes )com.yancao.qrscanner.ui.QrCodeOverlayView  
drawFrequency )com.yancao.qrscanner.ui.QrCodeOverlayView  drawQRCodeFrame )com.yancao.qrscanner.ui.QrCodeOverlayView  frameCounter )com.yancao.qrscanner.ui.QrCodeOverlayView  indexOfFirst )com.yancao.qrscanner.ui.QrCodeOverlayView  
invalidate )com.yancao.qrscanner.ui.QrCodeOverlayView  let )com.yancao.qrscanner.ui.QrCodeOverlayView  maxDisplayQRCount )com.yancao.qrscanner.ui.QrCodeOverlayView  
mutableListOf )com.yancao.qrscanner.ui.QrCodeOverlayView  qrDisplayDuration )com.yancao.qrscanner.ui.QrCodeOverlayView  	removeAll )com.yancao.qrscanner.ui.QrCodeOverlayView  	scaleRect )com.yancao.qrscanner.ui.QrCodeOverlayView  scannedQRCodes )com.yancao.qrscanner.ui.QrCodeOverlayView  scannedQRPaint )com.yancao.qrscanner.ui.QrCodeOverlayView  setDrawFrequency )com.yancao.qrscanner.ui.QrCodeOverlayView  	textPaint )com.yancao.qrscanner.ui.QrCodeOverlayView  boundingBox 7com.yancao.qrscanner.ui.QrCodeOverlayView.ScannedQRInfo  content 7com.yancao.qrscanner.ui.QrCodeOverlayView.ScannedQRInfo  copy 7com.yancao.qrscanner.ui.QrCodeOverlayView.ScannedQRInfo  	timestamp 7com.yancao.qrscanner.ui.QrCodeOverlayView.ScannedQRInfo  ExperimentalGetImage &com.yancao.qrscanner.ui.QrScanActivity  Barcode com.yancao.qrscanner.viewModel  ExperimentalGetImage com.yancao.qrscanner.viewModel  Int com.yancao.qrscanner.viewModel  OptIn com.yancao.qrscanner.viewModel  Pair com.yancao.qrscanner.viewModel  getLatestResult com.yancao.qrscanner.viewModel  getResultCount com.yancao.qrscanner.viewModel  
hasResults com.yancao.qrscanner.viewModel  ExperimentalGetImage .com.yancao.qrscanner.viewModel.QrScanViewModel  Pair .com.yancao.qrscanner.viewModel.QrScanViewModel  getLatestResult .com.yancao.qrscanner.viewModel.QrScanViewModel  getResultCount .com.yancao.qrscanner.viewModel.QrScanViewModel  
hasResults .com.yancao.qrscanner.viewModel.QrScanViewModel  isCurrentlyScanning .com.yancao.qrscanner.viewModel.QrScanViewModel  qrCodePositions .com.yancao.qrscanner.viewModel.QrScanViewModel  	Function3 kotlin  minus kotlin.Float  plus kotlin.Float  invoke kotlin.Function3  inc 
kotlin.Int  minus 
kotlin.Int  rem 
kotlin.Int  
component1 kotlin.Pair  
component2 kotlin.Pair  length 
kotlin.String  indexOfFirst kotlin.collections  	removeAll kotlin.collections  get kotlin.collections.MutableList  indexOfFirst kotlin.collections.MutableList  	removeAll kotlin.collections.MutableList  removeAt kotlin.collections.MutableList  set kotlin.collections.MutableList  JvmOverloads 
kotlin.jvm  indexOfFirst kotlin.sequences  indexOfFirst kotlin.text  DetectedQRInfo android.graphics  Float android.graphics  List android.graphics  Path android.graphics  minOf android.graphics  until android.graphics  drawPath android.graphics.Canvas  close android.graphics.Path  lineTo android.graphics.Path  moveTo android.graphics.Path  bottom android.graphics.RectF  height android.graphics.RectF  left android.graphics.RectF  right android.graphics.RectF  top android.graphics.RectF  width android.graphics.RectF  DetectedQRInfo android.view.View  Path android.view.View  minOf android.view.View  until android.view.View  cornerPoints .com.google.mlkit.vision.barcode.common.Barcode  	emptyList com.yancao.qrscanner.camera  	emptyList .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  DetectedQRInfo com.yancao.qrscanner.ui  Float com.yancao.qrscanner.ui  List com.yancao.qrscanner.ui  Path com.yancao.qrscanner.ui  minOf com.yancao.qrscanner.ui  until com.yancao.qrscanner.ui  DetectedQRInfo )com.yancao.qrscanner.ui.QrCodeOverlayView  Float )com.yancao.qrscanner.ui.QrCodeOverlayView  List )com.yancao.qrscanner.ui.QrCodeOverlayView  Path )com.yancao.qrscanner.ui.QrCodeOverlayView  clearDetectedQRCodes )com.yancao.qrscanner.ui.QrCodeOverlayView  detectedQRCodes )com.yancao.qrscanner.ui.QrCodeOverlayView  drawQRCodeFromBarcode )com.yancao.qrscanner.ui.QrCodeOverlayView  drawQRFrame )com.yancao.qrscanner.ui.QrCodeOverlayView  drawTextNearPoint )com.yancao.qrscanner.ui.QrCodeOverlayView  height )com.yancao.qrscanner.ui.QrCodeOverlayView  minOf )com.yancao.qrscanner.ui.QrCodeOverlayView  until )com.yancao.qrscanner.ui.QrCodeOverlayView  updateDetectedQRCodes )com.yancao.qrscanner.ui.QrCodeOverlayView  width )com.yancao.qrscanner.ui.QrCodeOverlayView  barcode 8com.yancao.qrscanner.ui.QrCodeOverlayView.DetectedQRInfo  offsetX 8com.yancao.qrscanner.ui.QrCodeOverlayView.DetectedQRInfo  offsetY 8com.yancao.qrscanner.ui.QrCodeOverlayView.DetectedQRInfo  scaleFactor 8com.yancao.qrscanner.ui.QrCodeOverlayView.DetectedQRInfo  Nothing kotlin  size kotlin.Array  times kotlin.Float  IntIterator kotlin.collections  minOf kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  minOf kotlin.comparisons  	CharRange 
kotlin.ranges  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  	UIntRange 
kotlin.ranges  
ULongRange 
kotlin.ranges  until 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  minOf kotlin.sequences  minOf kotlin.text  clearResults android.app.Activity  clearResults android.content.Context  clearResults android.content.ContextWrapper  clearResults  android.view.ContextThemeWrapper  clearResults #androidx.activity.ComponentActivity  clearResults -androidx.activity.ComponentActivity.Companion  clearResults (androidx.appcompat.app.AppCompatActivity  clearResults #androidx.core.app.ComponentActivity  clearResults &androidx.fragment.app.FragmentActivity  clearResults com.yancao.qrscanner.ui  clearResults &com.yancao.qrscanner.ui.QrScanActivity  AspectRatio androidx.camera.core  setResolutionSelector *androidx.camera.core.ImageAnalysis.Builder  setResolutionSelector )androidx.camera.core.ImageCapture.Builder  setResolutionSelector $androidx.camera.core.Preview.Builder  AspectRatioStrategy 'androidx.camera.core.resolutionselector  ResolutionSelector 'androidx.camera.core.resolutionselector  !RATIO_16_9_FALLBACK_AUTO_STRATEGY ;androidx.camera.core.resolutionselector.AspectRatioStrategy  Builder :androidx.camera.core.resolutionselector.ResolutionSelector  build Bandroidx.camera.core.resolutionselector.ResolutionSelector.Builder  setAspectRatioStrategy Bandroidx.camera.core.resolutionselector.ResolutionSelector.Builder  AspectRatioStrategy com.yancao.qrscanner.camera  ResolutionSelector com.yancao.qrscanner.camera  AspectRatioStrategy )com.yancao.qrscanner.camera.CameraManager  ResolutionSelector )com.yancao.qrscanner.camera.CameraManager  Size android.util  ResolutionStrategy 'androidx.camera.core.resolutionselector  setResolutionStrategy Bandroidx.camera.core.resolutionselector.ResolutionSelector.Builder  'FALLBACK_RULE_CLOSEST_HIGHER_THEN_LOWER :androidx.camera.core.resolutionselector.ResolutionStrategy  ResolutionStrategy com.yancao.qrscanner.camera  Size com.yancao.qrscanner.camera  ResolutionStrategy )com.yancao.qrscanner.camera.CameraManager  Size )com.yancao.qrscanner.camera.CameraManager  Log android.graphics  forEachIndexed android.graphics  w android.util.Log  Log android.view.View  forEachIndexed android.view.View  UseCase com.yancao.qrscanner.camera  forEachIndexed com.yancao.qrscanner.ui  Log )com.yancao.qrscanner.ui.QrCodeOverlayView  forEachIndexed )com.yancao.qrscanner.ui.QrCodeOverlayView  forEachIndexed kotlin.collections  forEachIndexed kotlin.collections.List  forEachIndexed kotlin.sequences  forEachIndexed kotlin.text  surfaceProvider androidx.camera.core.Preview  'FALLBACK_RULE_CLOSEST_LOWER_THEN_HIGHER :androidx.camera.core.resolutionselector.ResolutionStrategy  INSTALL_PACKAGES android.Manifest.permission  ImageFormat android.graphics  YUV_420_888 android.graphics.ImageFormat  CameraCharacteristics android.hardware.camera2  Key .android.hardware.camera2.CameraCharacteristics  SCALER_STREAM_CONFIGURATION_MAP .android.hardware.camera2.CameraCharacteristics  StreamConfigurationMap android.hardware.camera2.params  getOutputSizes 6android.hardware.camera2.params.StreamConfigurationMap  height android.util.Size  width android.util.Size  Camera2CameraInfo androidx.camera.camera2.interop  ExperimentalCamera2Interop androidx.camera.camera2.interop  from 1androidx.camera.camera2.interop.Camera2CameraInfo  getCameraCharacteristic 1androidx.camera.camera2.interop.Camera2CameraInfo  
CameraInfo androidx.camera.core  ResolutionInfo androidx.camera.core  let "androidx.camera.core.ImageAnalysis  resolutionInfo "androidx.camera.core.ImageAnalysis  ResolutionFilter 'androidx.camera.core.resolutionselector  <SAM-CONSTRUCTOR> 8androidx.camera.core.resolutionselector.ResolutionFilter  setResolutionFilter Bandroidx.camera.core.resolutionselector.ResolutionSelector.Builder  
getCameraInfo /androidx.camera.lifecycle.ProcessCameraProvider  Camera2CameraInfo com.yancao.qrscanner.camera  CameraCharacteristics com.yancao.qrscanner.camera  ExperimentalCamera2Interop com.yancao.qrscanner.camera  ImageFormat com.yancao.qrscanner.camera  abs com.yancao.qrscanner.camera  firstOrNull com.yancao.qrscanner.camera  forEach com.yancao.qrscanner.camera  let com.yancao.qrscanner.camera  sortedBy com.yancao.qrscanner.camera  Camera2CameraInfo )com.yancao.qrscanner.camera.CameraManager  CameraCharacteristics )com.yancao.qrscanner.camera.CameraManager  ExperimentalCamera2Interop )com.yancao.qrscanner.camera.CameraManager  ImageFormat )com.yancao.qrscanner.camera.CameraManager  Log )com.yancao.qrscanner.camera.CameraManager  abs )com.yancao.qrscanner.camera.CameraManager  firstOrNull )com.yancao.qrscanner.camera.CameraManager  forEach )com.yancao.qrscanner.camera.CameraManager  let )com.yancao.qrscanner.camera.CameraManager  sortedBy )com.yancao.qrscanner.camera.CameraManager  (checkAndRequestInstallPackagesPermission ,com.yancao.qrscanner.utils.RequestPermission  sortedBy kotlin.collections  abs kotlin.math  sortedBy kotlin.sequences  let android.util.Size  listOf com.yancao.qrscanner.camera  minByOrNull com.yancao.qrscanner.camera  listOf )com.yancao.qrscanner.camera.CameraManager  minByOrNull )com.yancao.qrscanner.camera.CameraManager  listOf kotlin.collections  minByOrNull kotlin.collections  Entry kotlin.collections.Map  minByOrNull kotlin.sequences  minByOrNull kotlin.text  ifEmpty com.yancao.qrscanner.camera  map com.yancao.qrscanner.camera  take com.yancao.qrscanner.camera  ifEmpty )com.yancao.qrscanner.camera.CameraManager  map )com.yancao.qrscanner.camera.CameraManager  take )com.yancao.qrscanner.camera.CameraManager  Result kotlin  map kotlin  ifEmpty kotlin.collections  map kotlin.collections  take kotlin.collections  ifEmpty kotlin.collections.List  map kotlin.collections.List  take kotlin.collections.List  ifEmpty kotlin.sequences  map kotlin.sequences  take kotlin.sequences  ifEmpty kotlin.text  map kotlin.text  take kotlin.text  Log .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  Boolean android.app.Activity  Int android.app.Activity  SeekBar android.app.Activity  String android.app.Activity  format android.app.Activity  	viewModel android.app.Activity  OnSeekBarChangeListener android.app.Activity.SeekBar  Boolean android.content.Context  Int android.content.Context  SeekBar android.content.Context  String android.content.Context  format android.content.Context  	viewModel android.content.Context  OnSeekBarChangeListener android.content.Context.SeekBar  Boolean android.content.ContextWrapper  Int android.content.ContextWrapper  SeekBar android.content.ContextWrapper  String android.content.ContextWrapper  format android.content.ContextWrapper  	viewModel android.content.ContextWrapper  OnSeekBarChangeListener &android.content.ContextWrapper.SeekBar  Boolean  android.view.ContextThemeWrapper  Int  android.view.ContextThemeWrapper  SeekBar  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  format  android.view.ContextThemeWrapper  	viewModel  android.view.ContextThemeWrapper  OnSeekBarChangeListener (android.view.ContextThemeWrapper.SeekBar  	isEnabled android.view.View  SeekBar android.widget  	isEnabled android.widget.Button  progress android.widget.ProgressBar  OnSeekBarChangeListener android.widget.SeekBar  progress android.widget.SeekBar  setOnSeekBarChangeListener android.widget.SeekBar  Float #androidx.activity.ComponentActivity  Int #androidx.activity.ComponentActivity  SeekBar #androidx.activity.ComponentActivity  format #androidx.activity.ComponentActivity  	viewModel #androidx.activity.ComponentActivity  String -androidx.activity.ComponentActivity.Companion  format -androidx.activity.ComponentActivity.Companion  	viewModel -androidx.activity.ComponentActivity.Companion  OnSeekBarChangeListener +androidx.activity.ComponentActivity.SeekBar  Boolean (androidx.appcompat.app.AppCompatActivity  Int (androidx.appcompat.app.AppCompatActivity  SeekBar (androidx.appcompat.app.AppCompatActivity  String (androidx.appcompat.app.AppCompatActivity  format (androidx.appcompat.app.AppCompatActivity  	viewModel (androidx.appcompat.app.AppCompatActivity  OnSeekBarChangeListener 0androidx.appcompat.app.AppCompatActivity.SeekBar  
CameraControl androidx.camera.core  	ZoomState androidx.camera.core  
cameraControl androidx.camera.core.Camera  
cameraInfo androidx.camera.core.Camera  let androidx.camera.core.Camera  enableTorch "androidx.camera.core.CameraControl  setZoomRatio "androidx.camera.core.CameraControl  hasFlashUnit androidx.camera.core.CameraInfo  	zoomState androidx.camera.core.CameraInfo  maxZoomRatio androidx.camera.core.ZoomState  minZoomRatio androidx.camera.core.ZoomState  	zoomRatio androidx.camera.core.ZoomState  Float #androidx.core.app.ComponentActivity  Int #androidx.core.app.ComponentActivity  SeekBar #androidx.core.app.ComponentActivity  format #androidx.core.app.ComponentActivity  	viewModel #androidx.core.app.ComponentActivity  OnSeekBarChangeListener +androidx.core.app.ComponentActivity.SeekBar  Boolean &androidx.fragment.app.FragmentActivity  Int &androidx.fragment.app.FragmentActivity  SeekBar &androidx.fragment.app.FragmentActivity  String &androidx.fragment.app.FragmentActivity  format &androidx.fragment.app.FragmentActivity  	viewModel &androidx.fragment.app.FragmentActivity  OnSeekBarChangeListener .androidx.fragment.app.FragmentActivity.SeekBar  LiveData androidx.lifecycle  
coerceAtLeast androidx.lifecycle.ViewModel  coerceAtMost androidx.lifecycle.ViewModel  coerceIn androidx.lifecycle.ViewModel  Camera com.yancao.qrscanner.camera  
CameraControl com.yancao.qrscanner.camera  
CameraInfo com.yancao.qrscanner.camera  Float com.yancao.qrscanner.camera  coerceIn com.yancao.qrscanner.camera  
cameraControl )com.yancao.qrscanner.camera.CameraManager  
cameraInfo )com.yancao.qrscanner.camera.CameraManager  coerceIn )com.yancao.qrscanner.camera.CameraManager  getCurrentZoomRatio )com.yancao.qrscanner.camera.CameraManager  getMaxZoomRatio )com.yancao.qrscanner.camera.CameraManager  getMinZoomRatio )com.yancao.qrscanner.camera.CameraManager  
hasFlashlight )com.yancao.qrscanner.camera.CameraManager  
setFlashlight )com.yancao.qrscanner.camera.CameraManager  setZoomRatio )com.yancao.qrscanner.camera.CameraManager  
btnFlashlight 4com.yancao.qrscanner.databinding.ActivityMainBinding  	btnZoomIn 4com.yancao.qrscanner.databinding.ActivityMainBinding  
btnZoomOut 4com.yancao.qrscanner.databinding.ActivityMainBinding  btnZoomReset 4com.yancao.qrscanner.databinding.ActivityMainBinding  seekBarZoom 4com.yancao.qrscanner.databinding.ActivityMainBinding  tvZoomRange 4com.yancao.qrscanner.databinding.ActivityMainBinding  tvZoomRatio 4com.yancao.qrscanner.databinding.ActivityMainBinding  SeekBar com.yancao.qrscanner.ui  format com.yancao.qrscanner.ui  	viewModel com.yancao.qrscanner.ui  String &com.yancao.qrscanner.ui.QrScanActivity  format &com.yancao.qrscanner.ui.QrScanActivity  handleFlashlightButtonClick &com.yancao.qrscanner.ui.QrScanActivity  setupClickListeners &com.yancao.qrscanner.ui.QrScanActivity  setupObservers &com.yancao.qrscanner.ui.QrScanActivity  setupZoomControl &com.yancao.qrscanner.ui.QrScanActivity  updateFlashlightButtonText &com.yancao.qrscanner.ui.QrScanActivity  updateZoomDisplay &com.yancao.qrscanner.ui.QrScanActivity  updateZoomRange &com.yancao.qrscanner.ui.QrScanActivity  OnSeekBarChangeListener com.yancao.qrscanner.ui.SeekBar  CameraControlViewModel com.yancao.qrscanner.viewModel  Float com.yancao.qrscanner.viewModel  
coerceAtLeast com.yancao.qrscanner.viewModel  coerceAtMost com.yancao.qrscanner.viewModel  coerceIn com.yancao.qrscanner.viewModel  MutableLiveData 5com.yancao.qrscanner.viewModel.CameraControlViewModel  
coerceAtLeast 5com.yancao.qrscanner.viewModel.CameraControlViewModel  coerceAtMost 5com.yancao.qrscanner.viewModel.CameraControlViewModel  coerceIn 5com.yancao.qrscanner.viewModel.CameraControlViewModel  
hasFlashlight 5com.yancao.qrscanner.viewModel.CameraControlViewModel  isFlashlightEnabled 5com.yancao.qrscanner.viewModel.CameraControlViewModel  maxZoomRatio 5com.yancao.qrscanner.viewModel.CameraControlViewModel  minZoomRatio 5com.yancao.qrscanner.viewModel.CameraControlViewModel  	zoomRatio 5com.yancao.qrscanner.viewModel.CameraControlViewModel  
coerceAtLeast .com.yancao.qrscanner.viewModel.QrScanViewModel  coerceAtMost .com.yancao.qrscanner.viewModel.QrScanViewModel  
hasFlashlight .com.yancao.qrscanner.viewModel.QrScanViewModel  initializeCameraControlStates .com.yancao.qrscanner.viewModel.QrScanViewModel  isFlashlightEnabled .com.yancao.qrscanner.viewModel.QrScanViewModel  maxZoomRatio .com.yancao.qrscanner.viewModel.QrScanViewModel  minZoomRatio .com.yancao.qrscanner.viewModel.QrScanViewModel  	resetZoom .com.yancao.qrscanner.viewModel.QrScanViewModel  setZoomRatio .com.yancao.qrscanner.viewModel.QrScanViewModel  toggleFlashlight .com.yancao.qrscanner.viewModel.QrScanViewModel  zoomIn .com.yancao.qrscanner.viewModel.QrScanViewModel  zoomOut .com.yancao.qrscanner.viewModel.QrScanViewModel  	zoomRatio .com.yancao.qrscanner.viewModel.QrScanViewModel  String kotlin  
coerceAtLeast kotlin.Float  coerceAtMost kotlin.Float  coerceIn kotlin.Float  div 
kotlin.Int  	Companion 
kotlin.String  format 
kotlin.String  format kotlin.String.Companion  
coerceAtLeast 
kotlin.ranges  coerceAtMost 
kotlin.ranges  coerceIn 
kotlin.ranges  format kotlin.text  SuppressLint android.annotation  
ContextCompat android.app.Activity  Math android.app.Activity  MotionEvent android.app.Activity  R android.app.Activity  View android.app.Activity  minOf android.app.Activity  
ContextCompat android.content.Context  Math android.content.Context  MotionEvent android.content.Context  R android.content.Context  View android.content.Context  minOf android.content.Context  
ContextCompat android.content.ContextWrapper  Math android.content.ContextWrapper  MotionEvent android.content.ContextWrapper  R android.content.ContextWrapper  View android.content.ContextWrapper  minOf android.content.ContextWrapper  Drawable android.graphics.drawable  MotionEvent android.view  
ContextCompat  android.view.ContextThemeWrapper  Math  android.view.ContextThemeWrapper  MotionEvent  android.view.ContextThemeWrapper  R  android.view.ContextThemeWrapper  View  android.view.ContextThemeWrapper  minOf  android.view.ContextThemeWrapper  
ACTION_CANCEL android.view.MotionEvent  ACTION_DOWN android.view.MotionEvent  	ACTION_UP android.view.MotionEvent  action android.view.MotionEvent  GONE android.view.View  OnTouchListener android.view.View  VISIBLE android.view.View  
background android.view.View  setOnTouchListener android.view.View  
visibility android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnTouchListener  LinearLayout android.widget  
background android.widget.Button  let android.widget.Button  setOnTouchListener android.widget.FrameLayout  
visibility android.widget.LinearLayout  Button #androidx.activity.ComponentActivity  
ContextCompat #androidx.activity.ComponentActivity  Math #androidx.activity.ComponentActivity  MotionEvent #androidx.activity.ComponentActivity  R #androidx.activity.ComponentActivity  SuppressLint #androidx.activity.ComponentActivity  View #androidx.activity.ComponentActivity  minOf #androidx.activity.ComponentActivity  
ContextCompat -androidx.activity.ComponentActivity.Companion  Math -androidx.activity.ComponentActivity.Companion  MotionEvent -androidx.activity.ComponentActivity.Companion  R -androidx.activity.ComponentActivity.Companion  View -androidx.activity.ComponentActivity.Companion  minOf -androidx.activity.ComponentActivity.Companion  
ContextCompat (androidx.appcompat.app.AppCompatActivity  Math (androidx.appcompat.app.AppCompatActivity  MotionEvent (androidx.appcompat.app.AppCompatActivity  R (androidx.appcompat.app.AppCompatActivity  View (androidx.appcompat.app.AppCompatActivity  minOf (androidx.appcompat.app.AppCompatActivity  Button #androidx.core.app.ComponentActivity  
ContextCompat #androidx.core.app.ComponentActivity  Math #androidx.core.app.ComponentActivity  MotionEvent #androidx.core.app.ComponentActivity  R #androidx.core.app.ComponentActivity  SuppressLint #androidx.core.app.ComponentActivity  View #androidx.core.app.ComponentActivity  minOf #androidx.core.app.ComponentActivity  getDrawable #androidx.core.content.ContextCompat  
ContextCompat &androidx.fragment.app.FragmentActivity  Math &androidx.fragment.app.FragmentActivity  MotionEvent &androidx.fragment.app.FragmentActivity  R &androidx.fragment.app.FragmentActivity  View &androidx.fragment.app.FragmentActivity  minOf &androidx.fragment.app.FragmentActivity  R com.yancao.qrscanner  circle_button_selected com.yancao.qrscanner.R.drawable  circle_button_unselected com.yancao.qrscanner.R.drawable  
btnZoom10x 4com.yancao.qrscanner.databinding.ActivityMainBinding  	btnZoom1x 4com.yancao.qrscanner.databinding.ActivityMainBinding  	btnZoom5x 4com.yancao.qrscanner.databinding.ActivityMainBinding  zoomButtonsContainer 4com.yancao.qrscanner.databinding.ActivityMainBinding  zoomControlPanel 4com.yancao.qrscanner.databinding.ActivityMainBinding  zoomSliderContainer 4com.yancao.qrscanner.databinding.ActivityMainBinding  Button com.yancao.qrscanner.ui  
ContextCompat com.yancao.qrscanner.ui  Math com.yancao.qrscanner.ui  MotionEvent com.yancao.qrscanner.ui  R com.yancao.qrscanner.ui  SuppressLint com.yancao.qrscanner.ui  
ContextCompat &com.yancao.qrscanner.ui.QrScanActivity  Math &com.yancao.qrscanner.ui.QrScanActivity  MotionEvent &com.yancao.qrscanner.ui.QrScanActivity  R &com.yancao.qrscanner.ui.QrScanActivity  View &com.yancao.qrscanner.ui.QrScanActivity  currentSelectedZoomButton &com.yancao.qrscanner.ui.QrScanActivity  handleZoomButtonClick &com.yancao.qrscanner.ui.QrScanActivity  initializeZoomButtons &com.yancao.qrscanner.ui.QrScanActivity  isSliderMode &com.yancao.qrscanner.ui.QrScanActivity  let &com.yancao.qrscanner.ui.QrScanActivity  minOf &com.yancao.qrscanner.ui.QrScanActivity  setButtonSelected &com.yancao.qrscanner.ui.QrScanActivity  setButtonUnselected &com.yancao.qrscanner.ui.QrScanActivity  setupTouchHandling &com.yancao.qrscanner.ui.QrScanActivity  switchToButtonMode &com.yancao.qrscanner.ui.QrScanActivity  switchToSliderMode &com.yancao.qrscanner.ui.QrScanActivity  updateZoomButtonSelection &com.yancao.qrscanner.ui.QrScanActivity  updateZoomButtonStates &com.yancao.qrscanner.ui.QrScanActivity  abs java.lang.Math  
visibility android.widget.Button  updateControlsVisibility &com.yancao.qrscanner.ui.QrScanActivity  binding android.app.Activity  binding android.content.Context  binding android.content.ContextWrapper  binding  android.view.ContextThemeWrapper  binding #androidx.activity.ComponentActivity  binding -androidx.activity.ComponentActivity.Companion  binding (androidx.appcompat.app.AppCompatActivity  Builder #androidx.camera.core.CameraSelector  LENS_FACING_BACK #androidx.camera.core.CameraSelector  build +androidx.camera.core.CameraSelector.Builder  requireLensFacing +androidx.camera.core.CameraSelector.Builder  	hasCamera /androidx.camera.lifecycle.ProcessCameraProvider  binding #androidx.core.app.ComponentActivity  binding &androidx.fragment.app.FragmentActivity  android androidx.lifecycle.ViewModel  currentCameraSelector )com.yancao.qrscanner.camera.CameraManager  hasUltraWideCamera )com.yancao.qrscanner.camera.CameraManager  isUsingUltraWide )com.yancao.qrscanner.camera.CameraManager  isUsingUltraWideCamera )com.yancao.qrscanner.camera.CameraManager  switchToMainCamera )com.yancao.qrscanner.camera.CameraManager  switchToUltraWideCamera )com.yancao.qrscanner.camera.CameraManager  binding com.yancao.qrscanner.ui  Log &com.yancao.qrscanner.ui.QrScanActivity  android com.yancao.qrscanner.viewModel  android .com.yancao.qrscanner.viewModel.QrScanViewModel  hasUltraWideCamera .com.yancao.qrscanner.viewModel.QrScanViewModel  isUsingUltraWide .com.yancao.qrscanner.viewModel.QrScanViewModel  	smartZoom .com.yancao.qrscanner.viewModel.QrScanViewModel  switchCameraLens .com.yancao.qrscanner.viewModel.QrScanViewModel  Range android.util  Rational android.util  lower android.util.Range  upper android.util.Range  toFloat android.util.Rational  max android.widget.ProgressBar  max android.widget.SeekBar  
ExposureState androidx.camera.core  setExposureCompensationIndex "androidx.camera.core.CameraControl  
exposureState androidx.camera.core.CameraInfo  exposureCompensationIndex "androidx.camera.core.ExposureState  exposureCompensationRange "androidx.camera.core.ExposureState  exposureCompensationStep "androidx.camera.core.ExposureState  observe androidx.lifecycle.LiveData  Float androidx.lifecycle.ViewModel  getCurrentExposureCompensation )com.yancao.qrscanner.camera.CameraManager  getExposureCompensationStep )com.yancao.qrscanner.camera.CameraManager  getMaxExposureCompensation )com.yancao.qrscanner.camera.CameraManager  getMinExposureCompensation )com.yancao.qrscanner.camera.CameraManager  setExposureCompensation )com.yancao.qrscanner.camera.CameraManager  exposureControlPanel 4com.yancao.qrscanner.databinding.ActivityMainBinding  seekBarExposure 4com.yancao.qrscanner.databinding.ActivityMainBinding  tvExposureValue 4com.yancao.qrscanner.databinding.ActivityMainBinding  setupExposureControl &com.yancao.qrscanner.ui.QrScanActivity  LiveData com.yancao.qrscanner.viewModel  _exposureCompensation .com.yancao.qrscanner.viewModel.QrScanViewModel  _exposureCompensationStep .com.yancao.qrscanner.viewModel.QrScanViewModel  _maxExposureCompensation .com.yancao.qrscanner.viewModel.QrScanViewModel  _minExposureCompensation .com.yancao.qrscanner.viewModel.QrScanViewModel  exposureCompensation .com.yancao.qrscanner.viewModel.QrScanViewModel  maxExposureCompensation .com.yancao.qrscanner.viewModel.QrScanViewModel  minExposureCompensation .com.yancao.qrscanner.viewModel.QrScanViewModel  setExposureCompensation .com.yancao.qrscanner.viewModel.QrScanViewModel  
coerceAtLeast 
kotlin.Int  coerceAtMost 
kotlin.Int  coerceIn 
kotlin.Int  let 
kotlin.Int  
ValueAnimator android.animation  AnimatorUpdateListener android.animation.ValueAnimator  addUpdateListener android.animation.ValueAnimator  
animatedValue android.animation.ValueAnimator  duration android.animation.ValueAnimator  ofInt android.animation.ValueAnimator  start android.animation.ValueAnimator  <SAM-CONSTRUCTOR> 6android.animation.ValueAnimator.AnimatorUpdateListener  
ValueAnimator android.app.Activity  
ValueAnimator android.content.Context  
ValueAnimator android.content.ContextWrapper  
ValueAnimator  android.view.ContextThemeWrapper  let android.widget.SeekBar  
ValueAnimator #androidx.activity.ComponentActivity  
ValueAnimator -androidx.activity.ComponentActivity.Companion  
ValueAnimator (androidx.appcompat.app.AppCompatActivity  
ValueAnimator #androidx.core.app.ComponentActivity  
ValueAnimator &androidx.fragment.app.FragmentActivity  
ValueAnimator com.yancao.qrscanner.ui  
ValueAnimator &com.yancao.qrscanner.ui.QrScanActivity                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                