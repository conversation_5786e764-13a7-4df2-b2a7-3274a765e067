package com.yancao.qrscanner.ui

import android.annotation.SuppressLint
import android.app.Application
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.os.Bundle
import android.view.MotionEvent
import android.view.View
import android.widget.Button
import android.widget.SeekBar
import android.widget.Toast
import androidx.activity.viewModels
import androidx.annotation.OptIn
import androidx.appcompat.app.AppCompatActivity
import androidx.camera.core.ExperimentalGetImage
import androidx.core.content.ContextCompat
import com.yancao.qrscanner.R
import com.yancao.qrscanner.databinding.ActivityMainBinding
import com.yancao.qrscanner.domain.ScanResultsHolder
import com.yancao.qrscanner.utils.BroadCastUtils
import com.yancao.qrscanner.utils.RequestPermission
import com.yancao.qrscanner.viewModel.QrScanViewModel

/**
 * 二维码扫描主Activity
 *
 * 功能包括：
 * 1. 基础的二维码扫描功能
 * 2. 实时扫描控制
 * 3. 闪光灯控制
 * 4. 双模式焦段缩放控制：
 *    - 模式1：三个圆形按钮快速选择1x、5x、10x
 *    - 模式2：滑动条精确调节缩放
 * 5. 扫描结果显示和广播
 */
@ExperimentalGetImage
class QrScanActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMainBinding
    private val viewModel: QrScanViewModel by viewModels()
    private lateinit var permissionHelper: RequestPermission

    // 缩放控制相关变量
    private var currentSelectedZoomButton: Button? = null
    private var isSliderMode = false

    @OptIn(ExperimentalGetImage::class)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 设置覆盖层绘制频率
        binding.qrOverlayView.setDrawFrequency(5)

        // 初始化权限助手
        permissionHelper = RequestPermission(this)

        // 请求相机权限
        permissionHelper.checkAndRequestCameraPermission(
            onGranted = {
                // 权限获取成功，启动相机（默认不启用实时扫描）
                viewModel.startCamera(binding.previewView, this, enableRealtimeScanning = false)
            },
            onDenied = {
                Toast.makeText(this, "Camera permission denied", Toast.LENGTH_SHORT).show()
            }
        )

        setupObservers()
        setupClickListeners()
        setupZoomControl()
        setupExposureControl()
    }

    /**
     * 设置观察者
     */
    private fun setupObservers() {
        // 观察二维码位置信息
        viewModel.qrCodePositions.observe(this) { (barcodes, imageSizes) ->
            val (imageWidth, imageHeight) = imageSizes
            binding.qrOverlayView.updateDetectedQRCodes(
                barcodes,
                imageWidth,
                imageHeight
            )
        }

        // 观察扫描状态
        viewModel.isScanningEnabled.observe(this) { isScanning ->
            updateScanButtonText(isScanning)
            // 控制闪光灯按钮和缩放控制的显示/隐藏
            updateControlsVisibility(isScanning)
            // 停止扫描时清除绿框
            if (!isScanning) {
                binding.qrOverlayView.clearDetectedQRCodes()
            }
        }

        // 观察实时扫描结果
        viewModel.realtimeScanResults.observe(this) { results ->
            if (results.isNotEmpty()) {
                updateScanResultsDisplay()
            }
        }

        // 观察闪光灯状态
        viewModel.isFlashlightEnabled.observe(this) { isEnabled ->
            updateFlashlightButtonText(isEnabled)
        }

        // 观察闪光灯支持状态
        viewModel.hasFlashlight.observe(this) { hasFlash ->
            binding.btnFlashlight.isEnabled = hasFlash
            if (!hasFlash) {
                binding.btnFlashlight.text = "无闪光灯"
            }
        }

        // 观察缩放比例
        viewModel.zoomRatio.observe(this) { ratio ->
            updateZoomDisplay(ratio)
            // 更新SeekBar位置（避免循环调用）
            val progress = ((ratio - (viewModel.minZoomRatio.value ?: 1.0f)) /
                    ((viewModel.maxZoomRatio.value ?: 1.0f) - (viewModel.minZoomRatio.value
                        ?: 1.0f)) * 100).toInt()
            if (binding.seekBarZoom.progress != progress) {
                binding.seekBarZoom.progress = progress
            }
        }

        // 观察曝光补偿范围变化
        viewModel.minExposureCompensation.observe(this) { minExposure ->
            viewModel.maxExposureCompensation.observe(this) { maxExposure ->
                if (minExposure != null && maxExposure != null) {
                    // 设置滑动条范围（将负值转换为正值范围）
                    val range = maxExposure - minExposure
                    binding.seekBarExposure.max = range

                    // 设置当前值（中间位置对应曝光补偿0）
                    val currentExposure = viewModel.exposureCompensation.value ?: 0
                    binding.seekBarExposure.progress = currentExposure - minExposure
                }
            }
        }

        // 观察当前曝光补偿值变化
        viewModel.exposureCompensation.observe(this) { exposureIndex ->
            exposureIndex?.let {
                binding.tvExposureValue.text = it.toString()

                // 更新滑动条位置（避免循环调用）
                val minExposure = viewModel.minExposureCompensation.value ?: -2
                val expectedProgress = it - minExposure
                if (binding.seekBarExposure.progress != expectedProgress) {
                    binding.seekBarExposure.progress = expectedProgress
                }
            }
        }
    }

    /**
     * 设置点击监听器
     */
    private fun setupClickListeners() {
        // 扫描按钮
        binding.btnScan.setOnClickListener {
            handleScanButtonClick()
        }

        // 广播按钮
        binding.btnBroadcast.setOnClickListener {
            handleBroadcastButtonClick()
        }

        // 闪光灯按钮
        binding.btnFlashlight.setOnClickListener {
            handleFlashlightButtonClick()
        }


    }

    /**
     * 设置缩放控制
     */
    private fun setupZoomControl() {
        binding.seekBarZoom.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    val minRatio = viewModel.minZoomRatio.value ?: 1.0f
                    val maxRatio = viewModel.maxZoomRatio.value ?: 1.0f
                    val ratio = minRatio + (maxRatio - minRatio) * (progress / 100.0f)
                    viewModel.setZoomRatio(ratio)
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })
    }


    /**
     * 处理扫描按钮点击事件
     */
    @OptIn(ExperimentalGetImage::class)
    private fun handleScanButtonClick() {
        val isCurrentlyScanning = viewModel.isScanningEnabled.value ?: false

        if (isCurrentlyScanning) {
            viewModel.stopRealtimeScanning(binding.previewView, this)
            Toast.makeText(this, "已停止扫描", Toast.LENGTH_SHORT).show()
        } else {
            binding.tvScanResults.text = ""
            viewModel.startRealtimeScanning(binding.previewView, this)
            Toast.makeText(this, "开始扫描二维码...", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 处理广播按钮点击事件
     */
    @OptIn(ExperimentalGetImage::class)
    private fun handleBroadcastButtonClick() {
        val scanResults = viewModel.getAllScanResults()

        if (scanResults.isEmpty()) {
            Toast.makeText(this, "没有扫描结果可以广播", Toast.LENGTH_SHORT).show()
            return
        }

        val latestResult = ScanResultsHolder.getLatestResult() ?: ""

        scanResults.forEach { result ->
            val broadcastIntent = Intent(BroadCastUtils.BROADCAST_ACTION).apply {
                putExtra(BroadCastUtils.BROADCAST_RAW_DATA, result)
            }
            sendBroadcast(broadcastIntent)
        }

        Toast.makeText(this, "已广播 ${scanResults.size} 个扫描结果", Toast.LENGTH_SHORT).show()
    }

    /**
     * 处理闪光灯按钮点击事件
     */
    private fun handleFlashlightButtonClick() {
        if (viewModel.hasFlashlight.value == true) {
            val success = viewModel.toggleFlashlight()
            if (!success) {
                Toast.makeText(this, "闪光灯控制失败", Toast.LENGTH_SHORT).show()
            }
        } else {
            Toast.makeText(this, "设备不支持闪光灯", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 更新扫描结果显示
     */
    @OptIn(ExperimentalGetImage::class)
    private fun updateScanResultsDisplay() {
        val results = viewModel.getAllScanResults()
        val displayText = if (results.isEmpty()) {
            "扫描结果将显示在这里..."
        } else {
            "共扫描到 ${results.size} 个结果：${results.takeLast(2).joinToString(", ")}" +
                    if (results.size > 2) "..." else ""
        }
        binding.tvScanResults.text = displayText
    }

    /**
     * 更新扫描按钮文本
     */
    private fun updateScanButtonText(isScanning: Boolean) {
        binding.btnScan.text = if (isScanning) "停止扫描" else "开始扫描"
    }

    /**
     * 更新闪光灯按钮文本
     */
    private fun updateFlashlightButtonText(isEnabled: Boolean) {
        binding.btnFlashlight.text = if (isEnabled) "关闭闪光灯" else "开启闪光灯"
    }

    /**
     * 更新缩放显示
     */
    private fun updateZoomDisplay(ratio: Float) {
        binding.tvZoomRatio.text = "缩放: ${String.format("%.1f", ratio)}x"
    }

    /**
     * 更新控件可见性
     * @param isScanning 是否正在扫描
     */
    private fun updateControlsVisibility(isScanning: Boolean) {
        val visibility = if (isScanning) View.VISIBLE else View.GONE
        binding.btnFlashlight.visibility = visibility
        binding.zoomSliderContainer.visibility = visibility
        binding.exposureControlPanel.visibility = visibility
    }


    /**
     * 设置曝光补偿控制
     */
    private fun setupExposureControl() {
        // 设置滑动条监听器
        binding.seekBarExposure.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    val minExposure = viewModel.minExposureCompensation.value ?: -2
                    val exposureIndex = minExposure + progress
                    viewModel.setExposureCompensation(exposureIndex)
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {
                // 开始拖拽时的处理（可选）
            }

            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                // 停止拖拽时的处理（可选）
            }
        })
    }

    override fun onDestroy() {
        super.onDestroy()
        // 可以选择在Activity销毁时清空结果，或者保留给其他模块使用
        ScanResultsHolder.clearResults()
    }
}